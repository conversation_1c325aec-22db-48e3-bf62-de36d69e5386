# Kiến trúc TikTok Automation Framework

## Tổng quan

TikTok Automation Framework được thiết kế theo kiến trúc modular với các thành phần độc lập, dễ bảo trì và mở rộng.

## Sơ đồ Kiến trúc

```
┌─────────────────────────────────────────────────────────────┐
│                    TikTok Automation Framework              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Automation      │    │ Human Behavior  │                │
│  │ Engine          │◄──►│ Simulator       │                │
│  │                 │    │                 │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Account         │    │ Rate Limiter    │                │
│  │ Manager         │    │                 │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Config          │    │ Proxy Manager   │                │
│  │ Manager         │    │                 │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Logger          │    │ Browser Driver  │                │
│  │                 │    │ (Selenium)      │                │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Thành phần Chính

### 1. Automation Engine (`automation_engine.py`)
- **Chức năng**: Mo<PERSON><PERSON> chính điều phối toàn bộ automation
- **Trách nhiệm**:
  - Quản lý session
  - Điều phối các module khác
  - Xử lý lỗi và safety features
  - Browser management

### 2. Human Behavior Simulator (`human_behavior.py`)
- **Chức năng**: Mô phỏng hành vi người dùng thực tế
- **Trách nhiệm**:
  - Random delays và timing
  - Mouse movement simulation
  - Typing patterns
  - Scroll behaviors
  - Reading/watching time simulation

### 3. Account Manager (`account_manager.py`)
- **Chức năng**: Quản lý nhiều tài khoản
- **Trách nhiệm**:
  - Account rotation
  - Account statistics tracking
  - Warm-up period management
  - Error tracking per account

### 4. Rate Limiter (`rate_limiter.py`)
- **Chức năng**: Kiểm soát tốc độ thực hiện actions
- **Trách nhiệm**:
  - Hourly/daily rate limiting
  - Action tracking
  - Wait time calculation
  - Data persistence

### 5. Config Manager (`config_manager.py`)
- **Chức năng**: Quản lý cấu hình hệ thống
- **Trách nhiệm**:
  - Load/parse YAML configs
  - Provide typed config objects
  - Default value handling
  - Config validation

### 6. Proxy Manager (`proxy_manager.py`)
- **Chức năng**: Quản lý proxy rotation
- **Trách nhiệm**:
  - Proxy health checking
  - Automatic rotation
  - IP management
  - Failure handling

### 7. Logger (`logger.py`)
- **Chức năng**: Hệ thống logging comprehensive
- **Trách nhiệm**:
  - Structured logging
  - File rotation
  - Action tracking
  - Error reporting

## Luồng Dữ liệu

```
User Input → Config Manager → Automation Engine
                ↓
Account Manager ← → Rate Limiter
                ↓
Human Behavior ← → Browser Driver
                ↓
Proxy Manager → Network Requests
                ↓
Logger ← All Components
```

## Tính năng An toàn

### 1. Rate Limiting
- Hourly limits per action type
- Daily action limits
- Session duration limits
- Automatic cooldowns

### 2. Error Handling
- Retry mechanisms
- Error counting per account
- Automatic account deactivation
- Graceful degradation

### 3. Detection Avoidance
- Human-like timing patterns
- Random delays and variations
- User agent rotation
- Proxy support

### 4. Captcha Handling
- Automatic detection
- Session termination
- Account flagging
- Manual intervention support

## Cấu hình

### File Structure
```
config/
├── settings.yaml          # Main configuration
├── accounts.json          # Account information
├── comment_templates.yaml # Comment templates
└── user_agents.yaml      # User agent lists
```

### Data Persistence
```
data/
├── rate_limit_*.json     # Rate limiting data
├── session_*.json        # Session data
└── stats_*.json          # Statistics
```

## Extensibility

### Adding New Actions
1. Extend `automation_engine.py` with new methods
2. Add rate limiting rules in config
3. Update `human_behavior.py` for realistic timing
4. Add logging for the new action

### Adding New Platforms
1. Create platform-specific engine
2. Implement common interfaces
3. Reuse existing components (rate limiter, account manager)
4. Platform-specific configurations

### Custom Behaviors
1. Extend `HumanBehaviorSimulator`
2. Add new timing patterns
3. Implement platform-specific behaviors
4. Configure through YAML files

## Security Considerations

### Data Protection
- Encrypted password storage (recommended)
- Secure config file permissions
- Log sanitization
- Temporary data cleanup

### Network Security
- Proxy support for anonymity
- User agent rotation
- Request timing randomization
- Connection pooling

### Compliance
- Terms of Service respect
- Rate limiting enforcement
- Ethical usage guidelines
- Educational purpose emphasis

## Performance

### Optimization Strategies
- Lazy loading of components
- Efficient data structures
- Minimal memory footprint
- Fast startup times

### Scalability
- Multi-account support
- Concurrent session handling
- Resource pooling
- Horizontal scaling ready

## Testing Strategy

### Unit Tests
- Individual component testing
- Mock external dependencies
- Configuration validation
- Error condition testing

### Integration Tests
- Component interaction testing
- End-to-end workflows
- Browser automation testing
- Network failure simulation

### Safety Tests
- Rate limiting verification
- Error handling validation
- Captcha detection testing
- Account protection testing
