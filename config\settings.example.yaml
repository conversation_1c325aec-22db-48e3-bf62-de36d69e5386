# <PERSON><PERSON><PERSON> hình ch<PERSON>h cho TikTok Automation Framework

# <PERSON><PERSON><PERSON> h<PERSON>rowser
browser:
  type: "chrome"  # chrome, firefox, edge
  headless: false
  window_size: [1920, 1080]
  user_data_dir: "./browser_profiles"
  
# <PERSON><PERSON><PERSON> hình Human Behavior
human_behavior:
  scroll_delay:
    min: 2
    max: 5
  action_delay:
    min: 3
    max: 8
  typing_speed:
    min: 50
    max: 150  # milliseconds per character
  mouse_movement:
    enabled: true
    randomness: 0.3

# Rate Limiting
rate_limits:
  likes_per_hour: 30
  comments_per_hour: 10
  follows_per_hour: 20
  daily_actions_limit: 200
  session_duration: 3600  # seconds
  break_duration: 1800    # seconds

# Proxy Configuration
proxy:
  enabled: false
  rotation_interval: 300  # seconds
  providers:
    - type: "http"
      host: "proxy1.example.com"
      port: 8080
      username: ""
      password: ""

# Account Management
accounts:
  warm_up_period: 7  # days
  max_concurrent: 1
  rotation_schedule: "random"  # sequential, random

# Safety Features
safety:
  captcha_detection: true
  error_retry_attempts: 3
  cooldown_on_error: 300  # seconds
  max_daily_errors: 10

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file_rotation: true
  max_file_size: "10MB"
  backup_count: 5

# Target Configuration
targets:
  hashtags:
    - "#fyp"
    - "#viral"
    - "#trending"
  users:
    - "example_user1"
    - "example_user2"
  
# Comment Templates
comments:
  enabled: true
  templates_file: "config/comment_templates.yaml"
  personalization: true
