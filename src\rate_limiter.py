"""
Rate Limiter cho TikTok Automation Framework
Quản lý tốc độ thực hiện các action để tránh bị phát hiện
"""

import time
import json
from datetime import datetime, timedelta
from collections import defaultdict, deque
from pathlib import Path
from typing import Dict, Optional
from .logger import setup_logger
from .config_manager import RateLimitConfig

logger = setup_logger("RateLimiter")


class ActionTracker:
    """Theo dõi các action đã thực hiện"""
    
    def __init__(self):
        self.actions = defaultdict(deque)  # action_type -> deque of timestamps
        self.daily_counts = defaultdict(int)  # date -> count
        self.last_reset = datetime.now().date()
    
    def add_action(self, action_type: str):
        """Thêm một action vào tracker"""
        now = datetime.now()
        
        # Reset daily count nếu qua ngày mới
        if now.date() > self.last_reset:
            self.daily_counts.clear()
            self.last_reset = now.date()
        
        # Thêm timestamp
        self.actions[action_type].append(now)
        self.daily_counts[now.date()] += 1
        
        # Cleanup old timestamps (chỉ giữ 24h gần nhất)
        cutoff = now - timedelta(hours=24)
        while (self.actions[action_type] and 
               self.actions[action_type][0] < cutoff):
            self.actions[action_type].popleft()
    
    def get_hourly_count(self, action_type: str) -> int:
        """Lấy số lượng action trong 1 giờ qua"""
        now = datetime.now()
        cutoff = now - timedelta(hours=1)
        
        return sum(1 for ts in self.actions[action_type] if ts > cutoff)
    
    def get_daily_count(self) -> int:
        """Lấy tổng số action trong ngày"""
        today = datetime.now().date()
        return self.daily_counts.get(today, 0)
    
    def get_last_action_time(self, action_type: str) -> Optional[datetime]:
        """Lấy thời gian action cuối cùng"""
        if self.actions[action_type]:
            return self.actions[action_type][-1]
        return None


class RateLimiter:
    """Quản lý rate limiting cho automation"""
    
    def __init__(self, config: RateLimitConfig, data_dir: str = "data"):
        self.config = config
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.trackers = {}  # account_id -> ActionTracker
        self.session_start_times = {}  # account_id -> datetime
        
        self._load_data()
    
    def _get_data_file(self, account_id: str) -> Path:
        """Lấy đường dẫn file data cho account"""
        return self.data_dir / f"rate_limit_{account_id}.json"
    
    def _load_data(self):
        """Load dữ liệu rate limiting từ file"""
        try:
            for data_file in self.data_dir.glob("rate_limit_*.json"):
                account_id = data_file.stem.replace("rate_limit_", "")
                
                with open(data_file, 'r') as f:
                    data = json.load(f)
                
                tracker = ActionTracker()
                
                # Restore actions
                for action_type, timestamps in data.get('actions', {}).items():
                    for ts_str in timestamps:
                        ts = datetime.fromisoformat(ts_str)
                        # Chỉ giữ actions trong 24h qua
                        if datetime.now() - ts < timedelta(hours=24):
                            tracker.actions[action_type].append(ts)
                
                # Restore daily counts
                for date_str, count in data.get('daily_counts', {}).items():
                    date_obj = datetime.fromisoformat(date_str).date()
                    tracker.daily_counts[date_obj] = count
                
                self.trackers[account_id] = tracker
                logger.info(f"Loaded rate limit data for account: {account_id}")
                
        except Exception as e:
            logger.error(f"Error loading rate limit data: {e}")
    
    def _save_data(self, account_id: str):
        """Lưu dữ liệu rate limiting"""
        try:
            if account_id not in self.trackers:
                return
            
            tracker = self.trackers[account_id]
            data = {
                'actions': {},
                'daily_counts': {},
                'last_updated': datetime.now().isoformat()
            }
            
            # Save actions
            for action_type, timestamps in tracker.actions.items():
                data['actions'][action_type] = [
                    ts.isoformat() for ts in timestamps
                ]
            
            # Save daily counts
            for date_obj, count in tracker.daily_counts.items():
                data['daily_counts'][date_obj.isoformat()] = count
            
            data_file = self._get_data_file(account_id)
            with open(data_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving rate limit data: {e}")
    
    def _get_tracker(self, account_id: str) -> ActionTracker:
        """Lấy hoặc tạo tracker cho account"""
        if account_id not in self.trackers:
            self.trackers[account_id] = ActionTracker()
        return self.trackers[account_id]
    
    def can_perform_action(self, account_id: str, action_type: str) -> bool:
        """Kiểm tra có thể thực hiện action không"""
        tracker = self._get_tracker(account_id)
        
        # Kiểm tra daily limit
        if tracker.get_daily_count() >= self.config.daily_actions_limit:
            logger.warning(f"Daily limit reached for {account_id}")
            return False
        
        # Kiểm tra hourly limits
        hourly_count = tracker.get_hourly_count(action_type)
        
        limits = {
            'like': self.config.likes_per_hour,
            'comment': self.config.comments_per_hour,
            'follow': self.config.follows_per_hour,
            'share': self.config.likes_per_hour,  # Dùng chung với like
            'repost': self.config.comments_per_hour  # Dùng chung với comment
        }
        
        limit = limits.get(action_type, 10)  # Default limit
        
        if hourly_count >= limit:
            logger.warning(f"Hourly limit reached for {action_type}: {hourly_count}/{limit}")
            return False
        
        return True
    
    def get_wait_time(self, account_id: str, action_type: str) -> float:
        """Tính thời gian cần chờ trước khi thực hiện action tiếp theo"""
        tracker = self._get_tracker(account_id)
        
        # Kiểm tra thời gian action cuối
        last_action = tracker.get_last_action_time(action_type)
        if last_action:
            time_since_last = (datetime.now() - last_action).total_seconds()
            min_interval = 60  # Tối thiểu 1 phút giữa các action cùng loại
            
            if time_since_last < min_interval:
                return min_interval - time_since_last
        
        return 0
    
    def record_action(self, account_id: str, action_type: str):
        """Ghi nhận một action đã được thực hiện"""
        tracker = self._get_tracker(account_id)
        tracker.add_action(action_type)
        
        # Lưu data
        self._save_data(account_id)
        
        logger.info(f"Recorded action: {action_type} for {account_id}")
    
    def start_session(self, account_id: str):
        """Bắt đầu session cho account"""
        self.session_start_times[account_id] = datetime.now()
        logger.info(f"Started session for {account_id}")
    
    def should_end_session(self, account_id: str) -> bool:
        """Kiểm tra có nên kết thúc session không"""
        if account_id not in self.session_start_times:
            return False
        
        session_duration = (datetime.now() - 
                          self.session_start_times[account_id]).total_seconds()
        
        return session_duration >= self.config.session_duration
    
    def get_session_stats(self, account_id: str) -> Dict:
        """Lấy thống kê session"""
        tracker = self._get_tracker(account_id)
        
        stats = {
            'daily_actions': tracker.get_daily_count(),
            'daily_limit': self.config.daily_actions_limit,
            'hourly_stats': {}
        }
        
        # Thống kê theo từng loại action
        for action_type in ['like', 'comment', 'follow', 'share']:
            hourly_count = tracker.get_hourly_count(action_type)
            limits = {
                'like': self.config.likes_per_hour,
                'comment': self.config.comments_per_hour,
                'follow': self.config.follows_per_hour,
                'share': self.config.likes_per_hour
            }
            
            stats['hourly_stats'][action_type] = {
                'count': hourly_count,
                'limit': limits.get(action_type, 10)
            }
        
        # Session duration
        if account_id in self.session_start_times:
            session_duration = (datetime.now() - 
                              self.session_start_times[account_id]).total_seconds()
            stats['session_duration'] = session_duration
            stats['session_limit'] = self.config.session_duration
        
        return stats
    
    def cleanup_old_data(self):
        """Dọn dẹp dữ liệu cũ"""
        try:
            cutoff_date = datetime.now().date() - timedelta(days=7)
            
            for account_id, tracker in self.trackers.items():
                # Xóa daily counts cũ
                old_dates = [date for date in tracker.daily_counts.keys() 
                           if date < cutoff_date]
                for date in old_dates:
                    del tracker.daily_counts[date]
                
                self._save_data(account_id)
            
            logger.info("Cleaned up old rate limit data")
            
        except Exception as e:
            logger.error(f"Error cleaning up data: {e}")
