"""
<PERSON><PERSON> thống logging cho TikTok Automation Framework
"""

import logging
import logging.handlers
import os
from datetime import datetime
import colorlog
from pathlib import Path


class AutomationLogger:
    """Logger chuyên dụng cho automation framework"""
    
    def __init__(self, name="TikTokAutomation", log_dir="logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Tạo logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # Tránh duplicate handlers
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """Thiết lập các handler cho logger"""
        
        # Console handler với màu sắc
        console_handler = colorlog.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        console_format = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler.setFormatter(console_format)
        
        # File handler cho tất cả logs
        log_file = self.log_dir / f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        file_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_format)
        
        # Error handler riêng
        error_file = self.log_dir / f"{self.name}_errors_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_format)
        
        # Thêm handlers
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
    
    def get_logger(self):
        """Trả về logger instance"""
        return self.logger
    
    def log_action(self, action, account=None, details=None):
        """Log một action cụ thể"""
        message = f"ACTION: {action}"
        if account:
            message += f" | Account: {account}"
        if details:
            message += f" | Details: {details}"
        
        self.logger.info(message)
    
    def log_error(self, error, context=None):
        """Log lỗi với context"""
        message = f"ERROR: {str(error)}"
        if context:
            message += f" | Context: {context}"
        
        self.logger.error(message, exc_info=True)
    
    def log_rate_limit(self, action, limit_type, wait_time):
        """Log khi hit rate limit"""
        self.logger.warning(
            f"RATE_LIMIT: {action} | Type: {limit_type} | Wait: {wait_time}s"
        )
    
    def log_captcha_detected(self, account=None):
        """Log khi phát hiện captcha"""
        message = "CAPTCHA_DETECTED"
        if account:
            message += f" | Account: {account}"
        
        self.logger.warning(message)
    
    def log_session_start(self, account, session_id):
        """Log bắt đầu session"""
        self.logger.info(f"SESSION_START | Account: {account} | ID: {session_id}")
    
    def log_session_end(self, account, session_id, duration, actions_count):
        """Log kết thúc session"""
        self.logger.info(
            f"SESSION_END | Account: {account} | ID: {session_id} | "
            f"Duration: {duration}s | Actions: {actions_count}"
        )


def setup_logger(name="TikTokAutomation", log_dir="logs", level=logging.INFO):
    """
    Thiết lập logger đơn giản
    
    Args:
        name: Tên logger
        log_dir: Thư mục chứa log files
        level: Mức độ logging
    
    Returns:
        Logger instance
    """
    automation_logger = AutomationLogger(name, log_dir)
    logger = automation_logger.get_logger()
    logger.setLevel(level)
    
    return logger


# Tạo logger mặc định
default_logger = setup_logger()
