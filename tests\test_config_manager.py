"""
Unit tests cho ConfigManager
"""

import unittest
import tempfile
import os
import yaml
from pathlib import Path
import sys

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.config_manager import ConfigManager, <PERSON>rowserConfig, HumanBehaviorConfig


class TestConfigManager(unittest.TestCase):
    """Test cases cho ConfigManager"""
    
    def setUp(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_dir = Path(self.temp_dir) / "config"
        self.config_dir.mkdir(exist_ok=True)
        
        # Create test config file
        self.test_config = {
            'browser': {
                'type': 'chrome',
                'headless': True,
                'window_size': [1280, 720],
                'user_data_dir': './test_profiles'
            },
            'human_behavior': {
                'scroll_delay': {'min': 1, 'max': 3},
                'action_delay': {'min': 2, 'max': 5},
                'typing_speed': {'min': 100, 'max': 200},
                'mouse_movement': {'enabled': False, 'randomness': 0.5}
            },
            'rate_limits': {
                'likes_per_hour': 20,
                'comments_per_hour': 5,
                'follows_per_hour': 10,
                'daily_actions_limit': 100,
                'session_duration': 1800,
                'break_duration': 900
            },
            'safety': {
                'captcha_detection': True,
                'error_retry_attempts': 2,
                'cooldown_on_error': 180,
                'max_daily_errors': 5
            }
        }
        
        # Write test config
        config_file = self.config_dir / "settings.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(self.test_config, f)
    
    def tearDown(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_load_config(self):
        """Test loading configuration"""
        config_manager = ConfigManager(str(self.config_dir))
        
        # Test basic config loading
        self.assertEqual(config_manager.get('browser.type'), 'chrome')
        self.assertEqual(config_manager.get('browser.headless'), True)
        self.assertEqual(config_manager.get('rate_limits.likes_per_hour'), 20)
    
    def test_get_browser_config(self):
        """Test browser configuration"""
        config_manager = ConfigManager(str(self.config_dir))
        browser_config = config_manager.get_browser_config()
        
        self.assertIsInstance(browser_config, BrowserConfig)
        self.assertEqual(browser_config.type, 'chrome')
        self.assertEqual(browser_config.headless, True)
        self.assertEqual(browser_config.window_size, (1280, 720))
        self.assertEqual(browser_config.user_data_dir, './test_profiles')
    
    def test_get_human_behavior_config(self):
        """Test human behavior configuration"""
        config_manager = ConfigManager(str(self.config_dir))
        hb_config = config_manager.get_human_behavior_config()
        
        self.assertIsInstance(hb_config, HumanBehaviorConfig)
        self.assertEqual(hb_config.scroll_delay_min, 1)
        self.assertEqual(hb_config.scroll_delay_max, 3)
        self.assertEqual(hb_config.action_delay_min, 2)
        self.assertEqual(hb_config.action_delay_max, 5)
        self.assertEqual(hb_config.mouse_movement_enabled, False)
        self.assertEqual(hb_config.mouse_randomness, 0.5)
    
    def test_get_rate_limit_config(self):
        """Test rate limit configuration"""
        config_manager = ConfigManager(str(self.config_dir))
        rl_config = config_manager.get_rate_limit_config()
        
        self.assertEqual(rl_config.likes_per_hour, 20)
        self.assertEqual(rl_config.comments_per_hour, 5)
        self.assertEqual(rl_config.follows_per_hour, 10)
        self.assertEqual(rl_config.daily_actions_limit, 100)
        self.assertEqual(rl_config.session_duration, 1800)
        self.assertEqual(rl_config.break_duration, 900)
    
    def test_get_safety_config(self):
        """Test safety configuration"""
        config_manager = ConfigManager(str(self.config_dir))
        safety_config = config_manager.get_safety_config()
        
        self.assertEqual(safety_config.captcha_detection, True)
        self.assertEqual(safety_config.error_retry_attempts, 2)
        self.assertEqual(safety_config.cooldown_on_error, 180)
        self.assertEqual(safety_config.max_daily_errors, 5)
    
    def test_default_config(self):
        """Test default configuration when no file exists"""
        empty_dir = Path(self.temp_dir) / "empty"
        empty_dir.mkdir(exist_ok=True)
        
        config_manager = ConfigManager(str(empty_dir))
        
        # Should use default values
        browser_config = config_manager.get_browser_config()
        self.assertEqual(browser_config.type, 'chrome')
        self.assertEqual(browser_config.headless, False)
        self.assertEqual(browser_config.window_size, (1920, 1080))
    
    def test_nested_config_access(self):
        """Test nested configuration access"""
        config_manager = ConfigManager(str(self.config_dir))
        
        # Test nested access
        self.assertEqual(config_manager.get('browser.type'), 'chrome')
        self.assertEqual(config_manager.get('human_behavior.scroll_delay.min'), 1)
        self.assertEqual(config_manager.get('rate_limits.likes_per_hour'), 20)
        
        # Test non-existent keys
        self.assertIsNone(config_manager.get('non_existent.key'))
        self.assertEqual(config_manager.get('non_existent.key', 'default'), 'default')
    
    def test_comment_templates_loading(self):
        """Test comment templates loading"""
        # Create comment templates file
        templates = {
            'positive_comments': ['Great!', 'Awesome!'],
            'engagement_comments': ['Tell me more!', 'How did you do this?']
        }
        
        templates_file = self.config_dir / "comment_templates.yaml"
        with open(templates_file, 'w') as f:
            yaml.dump(templates, f)
        
        config_manager = ConfigManager(str(self.config_dir))
        loaded_templates = config_manager.get_comment_templates()
        
        self.assertEqual(loaded_templates['positive_comments'], ['Great!', 'Awesome!'])
        self.assertEqual(loaded_templates['engagement_comments'], ['Tell me more!', 'How did you do this?'])
    
    def test_user_agents_loading(self):
        """Test user agents loading"""
        # Create user agents file
        user_agents = {
            'mobile_user_agents': {
                'android': ['Mozilla/5.0 (Android...)'],
                'ios': ['Mozilla/5.0 (iPhone...)']
            }
        }
        
        ua_file = self.config_dir / "user_agents.yaml"
        with open(ua_file, 'w') as f:
            yaml.dump(user_agents, f)
        
        config_manager = ConfigManager(str(self.config_dir))
        loaded_ua = config_manager.get_user_agents()
        
        self.assertIn('mobile_user_agents', loaded_ua)
        self.assertIn('android', loaded_ua['mobile_user_agents'])
        self.assertIn('ios', loaded_ua['mobile_user_agents'])


if __name__ == '__main__':
    unittest.main()
