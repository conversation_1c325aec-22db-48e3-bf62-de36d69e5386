"""
Quản lý cấu hình cho TikTok Automation Framework
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass
from .logger import setup_logger

logger = setup_logger("ConfigManager")


@dataclass
class BrowserConfig:
    """Cấu hình browser"""
    type: str = "chrome"
    headless: bool = False
    window_size: tuple = (1920, 1080)
    user_data_dir: str = "./browser_profiles"


@dataclass
class HumanBehaviorConfig:
    """Cấu hình hành vi người dùng"""
    scroll_delay_min: int = 2
    scroll_delay_max: int = 5
    action_delay_min: int = 3
    action_delay_max: int = 8
    typing_speed_min: int = 50
    typing_speed_max: int = 150
    mouse_movement_enabled: bool = True
    mouse_randomness: float = 0.3


@dataclass
class RateLimitConfig:
    """Cấu hình rate limiting"""
    likes_per_hour: int = 30
    comments_per_hour: int = 10
    follows_per_hour: int = 20
    daily_actions_limit: int = 200
    session_duration: int = 3600
    break_duration: int = 1800


@dataclass
class SafetyConfig:
    """Cấu hình an toàn"""
    captcha_detection: bool = True
    error_retry_attempts: int = 3
    cooldown_on_error: int = 300
    max_daily_errors: int = 10


class ConfigManager:
    """Quản lý cấu hình tổng thể"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self._config = {}
        self._load_all_configs()
    
    def _load_all_configs(self):
        """Load tất cả file cấu hình"""
        try:
            # Load main settings
            settings_file = self.config_dir / "settings.yaml"
            if not settings_file.exists():
                settings_file = self.config_dir / "settings.example.yaml"
            
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f) or {}
                logger.info(f"Loaded config from {settings_file}")
            else:
                logger.warning("No settings file found, using defaults")
                self._config = self._get_default_config()
            
            # Load comment templates
            self._load_comment_templates()
            
            # Load user agents
            self._load_user_agents()
            
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            self._config = self._get_default_config()
    
    def _load_comment_templates(self):
        """Load comment templates"""
        try:
            templates_file = self.config_dir / "comment_templates.yaml"
            if templates_file.exists():
                with open(templates_file, 'r', encoding='utf-8') as f:
                    self._config['comment_templates'] = yaml.safe_load(f)
                logger.info("Loaded comment templates")
        except Exception as e:
            logger.error(f"Error loading comment templates: {e}")
    
    def _load_user_agents(self):
        """Load user agents"""
        try:
            ua_file = self.config_dir / "user_agents.yaml"
            if ua_file.exists():
                with open(ua_file, 'r', encoding='utf-8') as f:
                    self._config['user_agents'] = yaml.safe_load(f)
                logger.info("Loaded user agents")
        except Exception as e:
            logger.error(f"Error loading user agents: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Trả về cấu hình mặc định"""
        return {
            'browser': {
                'type': 'chrome',
                'headless': False,
                'window_size': [1920, 1080],
                'user_data_dir': './browser_profiles'
            },
            'human_behavior': {
                'scroll_delay': {'min': 2, 'max': 5},
                'action_delay': {'min': 3, 'max': 8},
                'typing_speed': {'min': 50, 'max': 150},
                'mouse_movement': {'enabled': True, 'randomness': 0.3}
            },
            'rate_limits': {
                'likes_per_hour': 30,
                'comments_per_hour': 10,
                'follows_per_hour': 20,
                'daily_actions_limit': 200,
                'session_duration': 3600,
                'break_duration': 1800
            },
            'safety': {
                'captcha_detection': True,
                'error_retry_attempts': 3,
                'cooldown_on_error': 300,
                'max_daily_errors': 10
            },
            'logging': {
                'level': 'INFO',
                'file_rotation': True,
                'max_file_size': '10MB',
                'backup_count': 5
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Lấy giá trị cấu hình"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_browser_config(self) -> BrowserConfig:
        """Lấy cấu hình browser"""
        browser_cfg = self.get('browser', {})
        return BrowserConfig(
            type=browser_cfg.get('type', 'chrome'),
            headless=browser_cfg.get('headless', False),
            window_size=tuple(browser_cfg.get('window_size', [1920, 1080])),
            user_data_dir=browser_cfg.get('user_data_dir', './browser_profiles')
        )
    
    def get_human_behavior_config(self) -> HumanBehaviorConfig:
        """Lấy cấu hình hành vi người dùng"""
        hb_cfg = self.get('human_behavior', {})
        scroll_delay = hb_cfg.get('scroll_delay', {})
        action_delay = hb_cfg.get('action_delay', {})
        typing_speed = hb_cfg.get('typing_speed', {})
        mouse_movement = hb_cfg.get('mouse_movement', {})
        
        return HumanBehaviorConfig(
            scroll_delay_min=scroll_delay.get('min', 2),
            scroll_delay_max=scroll_delay.get('max', 5),
            action_delay_min=action_delay.get('min', 3),
            action_delay_max=action_delay.get('max', 8),
            typing_speed_min=typing_speed.get('min', 50),
            typing_speed_max=typing_speed.get('max', 150),
            mouse_movement_enabled=mouse_movement.get('enabled', True),
            mouse_randomness=mouse_movement.get('randomness', 0.3)
        )
    
    def get_rate_limit_config(self) -> RateLimitConfig:
        """Lấy cấu hình rate limiting"""
        rl_cfg = self.get('rate_limits', {})
        return RateLimitConfig(
            likes_per_hour=rl_cfg.get('likes_per_hour', 30),
            comments_per_hour=rl_cfg.get('comments_per_hour', 10),
            follows_per_hour=rl_cfg.get('follows_per_hour', 20),
            daily_actions_limit=rl_cfg.get('daily_actions_limit', 200),
            session_duration=rl_cfg.get('session_duration', 3600),
            break_duration=rl_cfg.get('break_duration', 1800)
        )
    
    def get_safety_config(self) -> SafetyConfig:
        """Lấy cấu hình an toàn"""
        safety_cfg = self.get('safety', {})
        return SafetyConfig(
            captcha_detection=safety_cfg.get('captcha_detection', True),
            error_retry_attempts=safety_cfg.get('error_retry_attempts', 3),
            cooldown_on_error=safety_cfg.get('cooldown_on_error', 300),
            max_daily_errors=safety_cfg.get('max_daily_errors', 10)
        )
    
    def get_comment_templates(self) -> Dict[str, Any]:
        """Lấy comment templates"""
        return self.get('comment_templates', {})
    
    def get_user_agents(self) -> Dict[str, Any]:
        """Lấy user agents"""
        return self.get('user_agents', {})
