# Hướng dẫn Cài đặt

## <PERSON><PERSON><PERSON> c<PERSON><PERSON> thống

- Python 3.8+
- Chrome hoặc Firefox browser
- ChromeDriver hoặc GeckoDriver

## Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd TikTok
```

2. Tạo virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate  # Windows
```

3. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

4. Thiết lập dự án:
```bash
python setup_project.py
```

5. <PERSON><PERSON><PERSON> hình accounts:
- Chỉnh sửa `config/accounts.json`
- C<PERSON>p nhật `config/settings.yaml` nếu cần

## Chạy Tests

```bash
python run_tests.py
```

## Sử dụng

```bash
python examples/basic_usage.py
```
