"""
Unit tests cho RateLimiter
"""

import unittest
import tempfile
import time
import os
import sys
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.rate_limiter import RateLimiter, ActionTracker
from src.config_manager import RateLimitConfig


class TestActionTracker(unittest.TestCase):
    """Test cases cho ActionTracker"""
    
    def setUp(self):
        """Setup test environment"""
        self.tracker = ActionTracker()
    
    def test_add_action(self):
        """Test adding actions"""
        # Add some actions
        self.tracker.add_action('like')
        self.tracker.add_action('comment')
        self.tracker.add_action('like')
        
        # Check counts
        self.assertEqual(len(self.tracker.actions['like']), 2)
        self.assertEqual(len(self.tracker.actions['comment']), 1)
        self.assertEqual(self.tracker.get_daily_count(), 3)
    
    def test_hourly_count(self):
        """Test hourly action counting"""
        # Add actions
        for _ in range(5):
            self.tracker.add_action('like')
        
        # Should have 5 likes in the last hour
        self.assertEqual(self.tracker.get_hourly_count('like'), 5)
        self.assertEqual(self.tracker.get_hourly_count('comment'), 0)
    
    def test_last_action_time(self):
        """Test getting last action time"""
        # No actions yet
        self.assertIsNone(self.tracker.get_last_action_time('like'))
        
        # Add action
        before_time = datetime.now()
        self.tracker.add_action('like')
        after_time = datetime.now()
        
        last_time = self.tracker.get_last_action_time('like')
        self.assertIsNotNone(last_time)
        self.assertGreaterEqual(last_time, before_time)
        self.assertLessEqual(last_time, after_time)


class TestRateLimiter(unittest.TestCase):
    """Test cases cho RateLimiter"""
    
    def setUp(self):
        """Setup test environment"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test config
        self.config = RateLimitConfig(
            likes_per_hour=10,
            comments_per_hour=5,
            follows_per_hour=8,
            daily_actions_limit=50,
            session_duration=1800,
            break_duration=900
        )
        
        self.rate_limiter = RateLimiter(self.config, self.temp_dir)
        self.test_account = "test_user"
    
    def tearDown(self):
        """Cleanup test environment"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_can_perform_action_new_account(self):
        """Test action permission for new account"""
        # New account should be able to perform actions
        self.assertTrue(self.rate_limiter.can_perform_action(self.test_account, 'like'))
        self.assertTrue(self.rate_limiter.can_perform_action(self.test_account, 'comment'))
        self.assertTrue(self.rate_limiter.can_perform_action(self.test_account, 'follow'))
    
    def test_hourly_rate_limiting(self):
        """Test hourly rate limiting"""
        # Add actions up to the limit
        for _ in range(10):  # likes_per_hour = 10
            self.assertTrue(self.rate_limiter.can_perform_action(self.test_account, 'like'))
            self.rate_limiter.record_action(self.test_account, 'like')
        
        # Should not be able to add more
        self.assertFalse(self.rate_limiter.can_perform_action(self.test_account, 'like'))
    
    def test_daily_rate_limiting(self):
        """Test daily rate limiting"""
        # Add actions up to daily limit
        for i in range(50):  # daily_actions_limit = 50
            action_type = 'like' if i % 2 == 0 else 'comment'
            if self.rate_limiter.can_perform_action(self.test_account, action_type):
                self.rate_limiter.record_action(self.test_account, action_type)
        
        # Should not be able to add more actions
        self.assertFalse(self.rate_limiter.can_perform_action(self.test_account, 'like'))
        self.assertFalse(self.rate_limiter.can_perform_action(self.test_account, 'comment'))
    
    def test_wait_time_calculation(self):
        """Test wait time calculation"""
        # No previous actions, should be 0 wait time
        wait_time = self.rate_limiter.get_wait_time(self.test_account, 'like')
        self.assertEqual(wait_time, 0)
        
        # Record an action
        self.rate_limiter.record_action(self.test_account, 'like')
        
        # Should have some wait time now
        wait_time = self.rate_limiter.get_wait_time(self.test_account, 'like')
        self.assertGreater(wait_time, 0)
        self.assertLessEqual(wait_time, 60)  # Max 1 minute
    
    def test_session_management(self):
        """Test session management"""
        # Start session
        self.rate_limiter.start_session(self.test_account)
        
        # Should not end session immediately
        self.assertFalse(self.rate_limiter.should_end_session(self.test_account))
        
        # Manually set session start time to past
        self.rate_limiter.session_start_times[self.test_account] = (
            datetime.now() - timedelta(seconds=self.config.session_duration + 1)
        )
        
        # Should end session now
        self.assertTrue(self.rate_limiter.should_end_session(self.test_account))
    
    def test_session_stats(self):
        """Test session statistics"""
        # Record some actions
        self.rate_limiter.record_action(self.test_account, 'like')
        self.rate_limiter.record_action(self.test_account, 'comment')
        self.rate_limiter.record_action(self.test_account, 'like')
        
        # Get stats
        stats = self.rate_limiter.get_session_stats(self.test_account)
        
        # Check stats structure
        self.assertIn('daily_actions', stats)
        self.assertIn('daily_limit', stats)
        self.assertIn('hourly_stats', stats)
        
        # Check values
        self.assertEqual(stats['daily_actions'], 3)
        self.assertEqual(stats['daily_limit'], 50)
        self.assertEqual(stats['hourly_stats']['like']['count'], 2)
        self.assertEqual(stats['hourly_stats']['comment']['count'], 1)
    
    def test_data_persistence(self):
        """Test data saving and loading"""
        # Record some actions
        self.rate_limiter.record_action(self.test_account, 'like')
        self.rate_limiter.record_action(self.test_account, 'comment')
        
        # Create new rate limiter (should load saved data)
        new_rate_limiter = RateLimiter(self.config, self.temp_dir)
        
        # Check if data was loaded
        stats = new_rate_limiter.get_session_stats(self.test_account)
        self.assertGreater(stats['daily_actions'], 0)
    
    def test_multiple_accounts(self):
        """Test handling multiple accounts"""
        account1 = "user1"
        account2 = "user2"
        
        # Record actions for both accounts
        self.rate_limiter.record_action(account1, 'like')
        self.rate_limiter.record_action(account2, 'comment')
        self.rate_limiter.record_action(account1, 'like')
        
        # Check stats for each account
        stats1 = self.rate_limiter.get_session_stats(account1)
        stats2 = self.rate_limiter.get_session_stats(account2)
        
        self.assertEqual(stats1['daily_actions'], 2)
        self.assertEqual(stats2['daily_actions'], 1)
        self.assertEqual(stats1['hourly_stats']['like']['count'], 2)
        self.assertEqual(stats2['hourly_stats']['comment']['count'], 1)
    
    def test_cleanup_old_data(self):
        """Test cleanup of old data"""
        # Record some actions
        self.rate_limiter.record_action(self.test_account, 'like')
        
        # Run cleanup
        self.rate_limiter.cleanup_old_data()
        
        # Should still have recent data
        stats = self.rate_limiter.get_session_stats(self.test_account)
        self.assertGreater(stats['daily_actions'], 0)


if __name__ == '__main__':
    unittest.main()
