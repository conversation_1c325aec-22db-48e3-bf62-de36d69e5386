"""
Human Behavior Simulator cho TikTok Automation Framework
Mô phỏng hành vi người dùng thực tế để tránh bị phát hiện
"""

import random
import time
import math
from typing import Tuple, List, Optional
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement
from .logger import setup_logger
from .config_manager import HumanBehaviorConfig

logger = setup_logger("HumanBehavior")


class HumanBehaviorSimulator:
    """Mô phỏng hành vi người dùng thực tế"""
    
    def __init__(self, driver, config: HumanBehaviorConfig):
        self.driver = driver
        self.config = config
        self.action_chains = ActionChains(driver)
        
        # Tracking user behavior patterns
        self.scroll_patterns = []
        self.click_patterns = []
        self.typing_patterns = []
    
    def random_delay(self, min_seconds: Optional[float] = None, 
                    max_seconds: Optional[float] = None) -> float:
        """Tạo delay ngẫu nhiên giữa các action"""
        if min_seconds is None:
            min_seconds = self.config.action_delay_min
        if max_seconds is None:
            max_seconds = self.config.action_delay_max
        
        # Sử dụng phân phối normal để tạo delay tự nhiên hơn
        mean = (min_seconds + max_seconds) / 2
        std = (max_seconds - min_seconds) / 6  # 99.7% trong khoảng min-max
        
        delay = random.normalvariate(mean, std)
        delay = max(min_seconds, min(max_seconds, delay))
        
        logger.debug(f"Random delay: {delay:.2f}s")
        time.sleep(delay)
        return delay
    
    def human_scroll(self, direction: str = "down", distance: int = None) -> bool:
        """Scroll như người dùng thực tế"""
        try:
            if distance is None:
                # Random scroll distance
                distance = random.randint(300, 800)
            
            # Tạo scroll pattern tự nhiên (không đều)
            scroll_steps = random.randint(3, 7)
            step_size = distance // scroll_steps
            
            for i in range(scroll_steps):
                # Vary step size slightly
                current_step = step_size + random.randint(-50, 50)
                
                if direction == "down":
                    self.driver.execute_script(f"window.scrollBy(0, {current_step});")
                else:
                    self.driver.execute_script(f"window.scrollBy(0, -{current_step});")
                
                # Small delay between scroll steps
                time.sleep(random.uniform(0.1, 0.3))
            
            # Delay after scroll
            self.random_delay(
                self.config.scroll_delay_min,
                self.config.scroll_delay_max
            )
            
            logger.debug(f"Human scroll {direction}: {distance}px")
            return True
            
        except Exception as e:
            logger.error(f"Error in human_scroll: {e}")
            return False
    
    def human_click(self, element: WebElement, offset: bool = True) -> bool:
        """Click như người dùng thực tế"""
        try:
            # Move to element with human-like mouse movement
            if self.config.mouse_movement_enabled:
                self._move_to_element_humanly(element)
            
            # Add random offset to click position
            if offset:
                size = element.size
                location = element.location
                
                # Random offset within element bounds
                offset_x = random.randint(-size['width']//4, size['width']//4)
                offset_y = random.randint(-size['height']//4, size['height']//4)
                
                self.action_chains.move_to_element_with_offset(
                    element, offset_x, offset_y
                ).click().perform()
            else:
                self.action_chains.move_to_element(element).click().perform()
            
            # Delay after click
            self.random_delay()
            
            logger.debug("Human click performed")
            return True
            
        except Exception as e:
            logger.error(f"Error in human_click: {e}")
            return False
    
    def human_type(self, element: WebElement, text: str, 
                   clear_first: bool = True) -> bool:
        """Gõ text như người dùng thực tế"""
        try:
            if clear_first:
                element.clear()
                time.sleep(random.uniform(0.2, 0.5))
            
            # Click vào element trước
            self.human_click(element, offset=False)
            
            # Type với tốc độ tự nhiên
            for char in text:
                element.send_keys(char)
                
                # Random typing speed
                typing_delay = random.uniform(
                    self.config.typing_speed_min / 1000,
                    self.config.typing_speed_max / 1000
                )
                time.sleep(typing_delay)
                
                # Occasional longer pauses (thinking)
                if random.random() < 0.1:  # 10% chance
                    time.sleep(random.uniform(0.5, 1.5))
            
            # Delay after typing
            self.random_delay(1, 3)
            
            logger.debug(f"Human typed: {len(text)} characters")
            return True
            
        except Exception as e:
            logger.error(f"Error in human_type: {e}")
            return False
    
    def _move_to_element_humanly(self, element: WebElement):
        """Di chuyển chuột đến element một cách tự nhiên"""
        try:
            # Get current mouse position (approximate)
            current_pos = self._get_random_screen_position()
            
            # Get target position
            location = element.location
            size = element.size
            target_x = location['x'] + size['width'] // 2
            target_y = location['y'] + size['height'] // 2
            
            # Create curved path to target
            steps = random.randint(5, 15)
            
            for i in range(steps):
                progress = (i + 1) / steps
                
                # Add some randomness to the path
                randomness = self.config.mouse_randomness
                noise_x = random.uniform(-randomness, randomness) * 50
                noise_y = random.uniform(-randomness, randomness) * 50
                
                # Calculate intermediate position
                intermediate_x = current_pos[0] + (target_x - current_pos[0]) * progress + noise_x
                intermediate_y = current_pos[1] + (target_y - current_pos[1]) * progress + noise_y
                
                # Move to intermediate position
                self.action_chains.move_by_offset(
                    intermediate_x - current_pos[0],
                    intermediate_y - current_pos[1]
                ).perform()
                
                current_pos = (intermediate_x, intermediate_y)
                time.sleep(random.uniform(0.01, 0.05))
            
        except Exception as e:
            logger.debug(f"Mouse movement simulation failed: {e}")
    
    def _get_random_screen_position(self) -> Tuple[int, int]:
        """Lấy vị trí ngẫu nhiên trên màn hình"""
        window_size = self.driver.get_window_size()
        x = random.randint(0, window_size['width'])
        y = random.randint(0, window_size['height'])
        return (x, y)
    
    def simulate_reading_time(self, content_length: int = 100) -> float:
        """Mô phỏng thời gian đọc nội dung"""
        # Average reading speed: 200-300 words per minute
        # Assume 5 characters per word
        words = content_length / 5
        reading_speed = random.uniform(200, 300)  # words per minute
        
        base_time = (words / reading_speed) * 60  # seconds
        
        # Add randomness
        actual_time = base_time * random.uniform(0.5, 2.0)
        
        # Minimum and maximum reading time
        actual_time = max(2, min(30, actual_time))
        
        logger.debug(f"Simulated reading time: {actual_time:.2f}s")
        time.sleep(actual_time)
        return actual_time
    
    def simulate_video_watching(self, video_duration: int = None) -> float:
        """Mô phỏng xem video"""
        if video_duration is None:
            # TikTok videos are typically 15-60 seconds
            video_duration = random.randint(15, 60)
        
        # Watch percentage (people don't always watch full video)
        watch_percentage = random.uniform(0.3, 1.0)
        
        # Add some randomness to watch time
        watch_time = video_duration * watch_percentage
        watch_time *= random.uniform(0.8, 1.2)
        
        # Minimum watch time
        watch_time = max(3, watch_time)
        
        logger.debug(f"Simulated video watching: {watch_time:.2f}s")
        time.sleep(watch_time)
        return watch_time
    
    def random_pause(self, reason: str = "general"):
        """Tạo pause ngẫu nhiên với lý do cụ thể"""
        pause_times = {
            "thinking": (2, 8),
            "reading": (3, 10),
            "deciding": (1, 5),
            "general": (1, 4),
            "loading": (2, 6)
        }
        
        min_time, max_time = pause_times.get(reason, (1, 4))
        pause_duration = random.uniform(min_time, max_time)
        
        logger.debug(f"Random pause ({reason}): {pause_duration:.2f}s")
        time.sleep(pause_duration)
        return pause_duration
