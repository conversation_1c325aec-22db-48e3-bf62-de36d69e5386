# Hướng dẫn Sử dụng

⚠️ **CẢNH BÁO**: Framework này chỉ dành cho mục đích giáo dục!

## C<PERSON><PERSON> hình <PERSON> bản

### 1. <PERSON><PERSON><PERSON> hình Accounts

Chỉnh sửa `config/accounts.json`:

```json
{
  "your_username": {
    "username": "your_username",
    "password": "your_password",
    "email": "<EMAIL>",
    "is_active": true
  }
}
```

### 2. <PERSON><PERSON><PERSON> hình Settings

Chỉnh sửa `config/settings.yaml`:

```yaml
browser:
  type: "chrome"
  headless: false

rate_limits:
  likes_per_hour: 30
  comments_per_hour: 10
```

## Sử dụng Cơ bản

```python
from src.automation_engine import TikTokAutomation

# Khởi tạo
automation = TikTokAutomation()

# Chạy safe mode
automation.run_safe_mode(duration_minutes=10)
```

## Tính năng An toàn

- Rate limiting tự động
- Captcha detection
- Error handling
- Session management
- Human behavior simulation

## Tuân thủ Đạo đức

- Không spam
- Tôn trọng rate limits
- Không tạo nội dung có hại
- <PERSON><PERSON> thủ ToS của platform
