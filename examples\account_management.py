"""
Ví dụ quản lý tài khoản
⚠️ CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.account_manager import AccountManager, AccountInfo
from src.logger import setup_logger

logger = setup_logger("AccountManagement")


def demo_account_management():
    """Demo quản lý tài khoản"""
    
    print("👥 Account Management Demo")
    print("=" * 40)
    
    # Khởi tạo account manager
    account_manager = AccountManager()
    
    # Hiển thị accounts hiện tại
    print("\n📋 Current Accounts:")
    for username, account in account_manager.accounts.items():
        print(f"   {username}: Active={account.is_active}, Actions={account.total_actions}")
    
    # Thêm account mới (demo)
    print("\n➕ Adding demo account...")
    demo_account = AccountInfo(
        username="demo_user",
        password="demo_password",
        email="<EMAIL>",
        profile_name="Demo User"
    )
    
    if account_manager.add_account(demo_account):
        print("   ✅ Demo account added successfully")
    else:
        print("   ❌ Failed to add demo account")
    
    # Lấy active accounts
    active_accounts = account_manager.get_active_accounts()
    print(f"\n🟢 Active accounts: {len(active_accounts)}")
    for account in active_accounts:
        print(f"   - {account.username} (Ready: {account.is_ready_for_automation()})")
    
    # Demo rotation
    print("\n🔄 Account Rotation Demo:")
    for i in range(3):
        account = account_manager.rotate_account()
        if account:
            print(f"   Rotation {i+1}: {account.username}")
        else:
            print(f"   Rotation {i+1}: No account available")
    
    # Demo activity tracking
    print("\n📊 Activity Tracking Demo:")
    current_account = account_manager.get_current_account()
    if current_account:
        print(f"   Current account: {current_account.username}")
        
        # Simulate some activities
        account_manager.update_account_activity(current_account.username, "like")
        account_manager.update_account_activity(current_account.username, "comment")
        account_manager.update_account_activity(current_account.username, "like")
        
        # Show updated stats
        stats = account_manager.get_account_stats(current_account.username)
        print(f"   Updated stats: {stats}")
    
    # Total stats
    print("\n📈 Total Statistics:")
    total_stats = account_manager.get_total_stats()
    for key, value in total_stats.items():
        print(f"   {key}: {value}")


def demo_account_safety():
    """Demo tính năng an toàn"""
    
    print("\n🛡️ Safety Features Demo")
    print("=" * 40)
    
    account_manager = AccountManager()
    
    # Demo error recording
    print("\n❌ Error Recording Demo:")
    if account_manager.accounts:
        username = list(account_manager.accounts.keys())[0]
        
        # Record some errors
        account_manager.record_account_error(username, "Connection timeout")
        account_manager.record_account_error(username, "Element not found")
        
        account = account_manager.get_account(username)
        print(f"   Account {username} error count: {account.error_count}")
        print(f"   Last error: {account.last_error}")
    
    # Demo captcha recording
    print("\n🤖 Captcha Recording Demo:")
    if account_manager.accounts:
        username = list(account_manager.accounts.keys())[0]
        
        account_manager.record_account_captcha(username)
        
        account = account_manager.get_account(username)
        print(f"   Account {username} captcha count: {account.captcha_count}")
    
    # Demo account deactivation
    print("\n🚫 Account Deactivation Demo:")
    if len(account_manager.accounts) > 1:
        usernames = list(account_manager.accounts.keys())
        test_username = usernames[-1]  # Use last account for demo
        
        print(f"   Deactivating account: {test_username}")
        account_manager.deactivate_account(test_username, "Too many errors")
        
        account = account_manager.get_account(test_username)
        print(f"   Account active status: {account.is_active}")
        print(f"   Deactivation reason: {account.last_error}")


def main():
    """Main function"""
    
    print("🤖 TikTok Automation Framework - Account Management")
    print("⚠️  CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC!")
    print("-" * 60)
    
    try:
        demo_account_management()
        demo_account_safety()
        
        print("\n✅ Account management demo completed!")
        
    except Exception as e:
        logger.error(f"Error in demo: {e}")
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
