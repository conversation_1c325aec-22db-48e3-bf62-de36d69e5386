"""
TikTok Automation Engine - Mo<PERSON><PERSON> chính
⚠️ CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC
"""

import time
import random
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from .config_manager import ConfigManager
from .human_behavior import HumanBehaviorSimulator
from .account_manager import AccountManager
from .rate_limiter import RateLimiter
from .proxy_manager import ProxyManager, NoProxyManager
from .logger import setup_logger

logger = setup_logger("TikTokAutomation")


class TikTokAutomation:
    """
    TikTok Automation Engine chính
    ⚠️ CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC VÀ NGHIÊN CỨU
    """
    
    def __init__(self, config_dir: str = "config"):
        # Load configuration
        self.config = ConfigManager(config_dir)
        
        # Initialize components
        self.account_manager = AccountManager()
        self.rate_limiter = RateLimiter(self.config.get_rate_limit_config())
        
        # Initialize proxy manager
        proxy_config = self.config.get('proxy', {})
        if proxy_config.get('enabled', False):
            self.proxy_manager = ProxyManager(proxy_config.get('providers', []))
        else:
            self.proxy_manager = NoProxyManager()
        
        # Browser and automation components
        self.driver: Optional[webdriver.Chrome] = None
        self.human_behavior: Optional[HumanBehaviorSimulator] = None
        self.wait: Optional[WebDriverWait] = None
        
        # Session management
        self.session_id = str(uuid.uuid4())
        self.session_start_time = None
        self.current_account = None
        
        # Safety features
        self.safety_config = self.config.get_safety_config()
        self.error_count = 0
        self.captcha_detected = False
        
        logger.info("TikTok Automation Engine initialized")
        logger.warning("⚠️ CHỈ SỬ DỤNG CHO MỤC ĐÍCH GIÁO DỤC!")
    
    def setup_browser(self) -> bool:
        """Thiết lập browser với cấu hình an toàn"""
        try:
            browser_config = self.config.get_browser_config()
            
            if browser_config.type.lower() == "chrome":
                options = ChromeOptions()
                
                # Basic options
                if browser_config.headless:
                    options.add_argument("--headless")
                
                options.add_argument(f"--window-size={browser_config.window_size[0]},{browser_config.window_size[1]}")
                options.add_argument("--no-sandbox")
                options.add_argument("--disable-dev-shm-usage")
                options.add_argument("--disable-blink-features=AutomationControlled")
                options.add_experimental_option("excludeSwitches", ["enable-automation"])
                options.add_experimental_option('useAutomationExtension', False)
                
                # User agent
                user_agents = self.config.get_user_agents()
                if user_agents and 'mobile_user_agents' in user_agents:
                    mobile_agents = user_agents['mobile_user_agents']
                    if 'android' in mobile_agents:
                        user_agent = random.choice(mobile_agents['android'])
                        options.add_argument(f"--user-agent={user_agent}")
                
                # Proxy configuration
                proxy_config = self.proxy_manager.get_proxy_for_selenium()
                if proxy_config:
                    options.add_argument(f"--proxy-server={proxy_config['httpProxy']}")
                
                # Profile directory
                if browser_config.user_data_dir:
                    profile_dir = f"{browser_config.user_data_dir}/{self.current_account or 'default'}"
                    options.add_argument(f"--user-data-dir={profile_dir}")
                
                self.driver = webdriver.Chrome(options=options)
                
            else:
                raise ValueError(f"Unsupported browser: {browser_config.type}")
            
            # Configure driver
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(10)
            
            # Initialize components
            self.wait = WebDriverWait(self.driver, 10)
            self.human_behavior = HumanBehaviorSimulator(
                self.driver, 
                self.config.get_human_behavior_config()
            )
            
            logger.info("Browser setup completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error setting up browser: {e}")
            return False
    
    def start_session(self, account_username: str = None) -> bool:
        """Bắt đầu automation session"""
        try:
            # Rotate account if needed
            if account_username:
                account = self.account_manager.get_account(account_username)
                if not account or not account.is_ready_for_automation():
                    logger.error(f"Account {account_username} not ready for automation")
                    return False
                self.current_account = account_username
            else:
                account = self.account_manager.rotate_account()
                if not account:
                    logger.error("No accounts available for automation")
                    return False
                self.current_account = account.username
            
            # Setup browser
            if not self.setup_browser():
                return False
            
            # Start session tracking
            self.session_start_time = datetime.now()
            self.rate_limiter.start_session(self.current_account)
            
            logger.info(f"Session started for account: {self.current_account}")
            logger.info(f"Session ID: {self.session_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting session: {e}")
            return False
    
    def navigate_to_tiktok(self) -> bool:
        """Điều hướng đến TikTok"""
        try:
            logger.info("Navigating to TikTok...")
            
            # Navigate to TikTok
            self.driver.get("https://www.tiktok.com")
            
            # Wait for page load
            self.human_behavior.random_pause("loading")
            
            # Check if page loaded successfully
            try:
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                logger.info("Successfully navigated to TikTok")
                return True
            except TimeoutException:
                logger.error("Timeout waiting for TikTok to load")
                return False
                
        except Exception as e:
            logger.error(f"Error navigating to TikTok: {e}")
            return False
    
    def check_for_captcha(self) -> bool:
        """Kiểm tra có captcha không"""
        try:
            # Common captcha selectors
            captcha_selectors = [
                "[data-testid='captcha']",
                ".captcha",
                "#captcha",
                "[class*='captcha']",
                "[id*='captcha']"
            ]
            
            for selector in captcha_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed():
                        logger.warning("Captcha detected!")
                        self.captcha_detected = True
                        
                        if self.current_account:
                            self.account_manager.record_account_captcha(self.current_account)
                        
                        return True
                except NoSuchElementException:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking for captcha: {e}")
            return False
    
    def handle_error(self, error: Exception, context: str = ""):
        """Xử lý lỗi"""
        self.error_count += 1
        error_message = f"{context}: {str(error)}" if context else str(error)
        
        logger.error(f"Error occurred: {error_message}")
        
        # Record error for current account
        if self.current_account:
            self.account_manager.record_account_error(self.current_account, error_message)
        
        # Check if too many errors
        if self.error_count >= self.safety_config.max_daily_errors:
            logger.critical("Too many errors, stopping automation")
            self.stop_session()
            return False
        
        # Cooldown on error
        if self.safety_config.cooldown_on_error > 0:
            logger.info(f"Cooling down for {self.safety_config.cooldown_on_error} seconds")
            time.sleep(self.safety_config.cooldown_on_error)
        
        return True
    
    def should_continue_session(self) -> bool:
        """Kiểm tra có nên tiếp tục session không"""
        # Check session duration
        if self.rate_limiter.should_end_session(self.current_account):
            logger.info("Session duration limit reached")
            return False
        
        # Check for captcha
        if self.captcha_detected:
            logger.warning("Captcha detected, ending session")
            return False
        
        # Check error count
        if self.error_count >= self.safety_config.max_daily_errors:
            logger.warning("Too many errors, ending session")
            return False
        
        # Check if account is still ready
        if self.current_account:
            account = self.account_manager.get_account(self.current_account)
            if not account or not account.is_ready_for_automation():
                logger.warning("Account no longer ready for automation")
                return False
        
        return True
    
    def stop_session(self):
        """Kết thúc session"""
        try:
            if self.session_start_time:
                session_duration = (datetime.now() - self.session_start_time).total_seconds()
                logger.info(f"Session duration: {session_duration:.2f} seconds")
            
            # Close browser
            if self.driver:
                self.driver.quit()
                self.driver = None
            
            # Reset components
            self.human_behavior = None
            self.wait = None
            self.captcha_detected = False
            self.error_count = 0
            
            logger.info("Session stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping session: {e}")
    
    def run_safe_mode(self, duration_minutes: int = 30):
        """Chạy automation ở chế độ an toàn"""
        logger.info("🛡️ Starting SAFE MODE automation")
        logger.warning("⚠️ This is for EDUCATIONAL purposes only!")
        
        try:
            # Start session
            if not self.start_session():
                logger.error("Failed to start session")
                return False
            
            # Navigate to TikTok
            if not self.navigate_to_tiktok():
                logger.error("Failed to navigate to TikTok")
                return False
            
            # Run for specified duration
            end_time = time.time() + (duration_minutes * 60)
            
            while time.time() < end_time and self.should_continue_session():
                try:
                    # Check for captcha
                    if self.check_for_captcha():
                        break
                    
                    # Simulate browsing behavior
                    self._simulate_browsing()
                    
                    # Random break
                    self.human_behavior.random_pause("general")
                    
                except Exception as e:
                    if not self.handle_error(e, "safe_mode_loop"):
                        break
            
            logger.info("Safe mode automation completed")
            return True
            
        except Exception as e:
            logger.error(f"Error in safe mode: {e}")
            return False
        
        finally:
            self.stop_session()
    
    def _simulate_browsing(self):
        """Mô phỏng hành vi duyệt web cơ bản"""
        try:
            # Scroll down
            self.human_behavior.human_scroll("down")
            
            # Simulate reading/watching
            self.human_behavior.simulate_video_watching()
            
            # Random scroll up sometimes
            if random.random() < 0.3:
                self.human_behavior.human_scroll("up")
            
            logger.debug("Simulated browsing behavior")
            
        except Exception as e:
            logger.error(f"Error in browsing simulation: {e}")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Lấy thống kê session"""
        stats = {
            'session_id': self.session_id,
            'current_account': self.current_account,
            'session_start_time': self.session_start_time.isoformat() if self.session_start_time else None,
            'error_count': self.error_count,
            'captcha_detected': self.captcha_detected
        }
        
        if self.current_account:
            stats.update(self.rate_limiter.get_session_stats(self.current_account))
        
        return stats
