"""
Ví dụ sử dụng cơ bản TikTok Automation Framework
⚠️ CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.automation_engine import TikTokAutomation
from src.logger import setup_logger

logger = setup_logger("BasicUsage")


def main():
    """Ví dụ sử dụng cơ bản"""
    
    print("🤖 TikTok Automation Framework - Basic Usage")
    print("⚠️  CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC!")
    print("-" * 50)
    
    try:
        # Khởi tạo automation engine
        automation = TikTokAutomation()
        
        # Hiển thị thông tin cấu hình
        print("📋 Configuration loaded:")
        browser_config = automation.config.get_browser_config()
        print(f"   Browser: {browser_config.type}")
        print(f"   Headless: {browser_config.headless}")
        print(f"   Window size: {browser_config.window_size}")
        
        # Hiển thị thông tin accounts
        active_accounts = automation.account_manager.get_active_accounts()
        print(f"   Active accounts: {len(active_accounts)}")
        
        if not active_accounts:
            print("❌ No active accounts found!")
            print("   Please configure accounts in config/accounts.json")
            return
        
        # Hiển thị rate limits
        rate_config = automation.config.get_rate_limit_config()
        print(f"   Rate limits: {rate_config.likes_per_hour} likes/hour, {rate_config.comments_per_hour} comments/hour")
        
        print("\n🚀 Starting safe mode automation...")
        
        # Chạy automation ở chế độ an toàn
        success = automation.run_safe_mode(duration_minutes=5)  # Chạy 5 phút
        
        if success:
            print("✅ Automation completed successfully!")
        else:
            print("❌ Automation failed!")
        
        # Hiển thị thống kê
        stats = automation.get_session_stats()
        print("\n📊 Session Statistics:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
    except KeyboardInterrupt:
        print("\n⏹️  Automation stopped by user")
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        print(f"❌ Error: {e}")
    
    finally:
        print("\n👋 Goodbye!")


if __name__ == "__main__":
    main()
