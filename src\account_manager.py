"""
Account Manager cho TikTok Automation Framework
Quản lý nhiều tài khoản và rotation
"""

import json
import random
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from .logger import setup_logger

logger = setup_logger("AccountManager")


@dataclass
class AccountInfo:
    """Thông tin tài khoản"""
    username: str
    password: str
    email: str = ""
    phone: str = ""
    profile_name: str = ""
    
    # Account status
    is_active: bool = True
    is_verified: bool = False
    is_warmed_up: bool = False
    
    # Timestamps
    created_date: str = ""
    last_login: str = ""
    last_activity: str = ""
    warm_up_start: str = ""
    
    # Statistics
    total_actions: int = 0
    daily_actions: int = 0
    likes_count: int = 0
    comments_count: int = 0
    follows_count: int = 0
    
    # Safety
    error_count: int = 0
    captcha_count: int = 0
    last_error: str = ""
    
    # Browser profile
    browser_profile_path: str = ""
    user_agent: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Chuyển đổi thành dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AccountInfo':
        """Tạo từ dictionary"""
        return cls(**data)
    
    def is_ready_for_automation(self) -> bool:
        """Kiểm tra account có sẵn sàng cho automation không"""
        if not self.is_active:
            return False
        
        # Kiểm tra warm-up period
        if not self.is_warmed_up and self.warm_up_start:
            warm_up_start = datetime.fromisoformat(self.warm_up_start)
            if datetime.now() - warm_up_start < timedelta(days=7):
                return False
        
        # Kiểm tra error rate
        if self.error_count > 10:  # Quá nhiều lỗi
            return False
        
        return True
    
    def update_activity(self, action_type: str):
        """Cập nhật hoạt động"""
        self.last_activity = datetime.now().isoformat()
        self.total_actions += 1
        self.daily_actions += 1
        
        if action_type == "like":
            self.likes_count += 1
        elif action_type == "comment":
            self.comments_count += 1
        elif action_type == "follow":
            self.follows_count += 1
    
    def record_error(self, error_message: str):
        """Ghi nhận lỗi"""
        self.error_count += 1
        self.last_error = error_message
        logger.warning(f"Error recorded for {self.username}: {error_message}")
    
    def record_captcha(self):
        """Ghi nhận captcha"""
        self.captcha_count += 1
        logger.warning(f"Captcha recorded for {self.username}")
    
    def reset_daily_stats(self):
        """Reset thống kê hàng ngày"""
        self.daily_actions = 0


class AccountManager:
    """Quản lý nhiều tài khoản"""
    
    def __init__(self, accounts_file: str = "config/accounts.json", 
                 data_dir: str = "data"):
        self.accounts_file = Path(accounts_file)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.accounts: Dict[str, AccountInfo] = {}
        self.current_account: Optional[str] = None
        self.rotation_schedule = "random"  # random, sequential
        self.last_rotation = datetime.now()
        
        self._load_accounts()
    
    def _load_accounts(self):
        """Load accounts từ file"""
        try:
            if self.accounts_file.exists():
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    accounts_data = json.load(f)
                
                for username, account_data in accounts_data.items():
                    self.accounts[username] = AccountInfo.from_dict(account_data)
                
                logger.info(f"Loaded {len(self.accounts)} accounts")
            else:
                logger.warning(f"Accounts file not found: {self.accounts_file}")
                self._create_example_accounts_file()
                
        except Exception as e:
            logger.error(f"Error loading accounts: {e}")
    
    def _create_example_accounts_file(self):
        """Tạo file accounts mẫu"""
        example_accounts = {
            "example_user1": {
                "username": "example_user1",
                "password": "your_password_here",
                "email": "<EMAIL>",
                "phone": "",
                "profile_name": "User 1",
                "is_active": True,
                "is_verified": False,
                "is_warmed_up": False,
                "created_date": datetime.now().isoformat(),
                "last_login": "",
                "last_activity": "",
                "warm_up_start": "",
                "total_actions": 0,
                "daily_actions": 0,
                "likes_count": 0,
                "comments_count": 0,
                "follows_count": 0,
                "error_count": 0,
                "captcha_count": 0,
                "last_error": "",
                "browser_profile_path": "",
                "user_agent": ""
            }
        }
        
        try:
            self.accounts_file.parent.mkdir(exist_ok=True)
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(example_accounts, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Created example accounts file: {self.accounts_file}")
            
        except Exception as e:
            logger.error(f"Error creating example accounts file: {e}")
    
    def save_accounts(self):
        """Lưu accounts vào file"""
        try:
            accounts_data = {}
            for username, account in self.accounts.items():
                accounts_data[username] = account.to_dict()
            
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(accounts_data, f, indent=2, ensure_ascii=False)
            
            logger.debug("Accounts saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving accounts: {e}")
    
    def add_account(self, account_info: AccountInfo) -> bool:
        """Thêm account mới"""
        try:
            if account_info.username in self.accounts:
                logger.warning(f"Account {account_info.username} already exists")
                return False
            
            # Set default values
            if not account_info.created_date:
                account_info.created_date = datetime.now().isoformat()
            
            if not account_info.warm_up_start:
                account_info.warm_up_start = datetime.now().isoformat()
            
            self.accounts[account_info.username] = account_info
            self.save_accounts()
            
            logger.info(f"Added new account: {account_info.username}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding account: {e}")
            return False
    
    def get_account(self, username: str) -> Optional[AccountInfo]:
        """Lấy thông tin account"""
        return self.accounts.get(username)
    
    def get_active_accounts(self) -> List[AccountInfo]:
        """Lấy danh sách accounts active"""
        return [account for account in self.accounts.values() 
                if account.is_active and account.is_ready_for_automation()]
    
    def get_current_account(self) -> Optional[AccountInfo]:
        """Lấy account hiện tại"""
        if self.current_account:
            return self.accounts.get(self.current_account)
        return None
    
    def rotate_account(self, force: bool = False) -> Optional[AccountInfo]:
        """Rotate sang account tiếp theo"""
        active_accounts = self.get_active_accounts()
        
        if not active_accounts:
            logger.warning("No active accounts available")
            return None
        
        if len(active_accounts) == 1:
            self.current_account = active_accounts[0].username
            return active_accounts[0]
        
        # Kiểm tra có cần rotate không
        if not force and self.current_account:
            current_account = self.get_current_account()
            if current_account and current_account.is_ready_for_automation():
                # Kiểm tra thời gian session
                if self.last_rotation and (datetime.now() - self.last_rotation).seconds < 3600:
                    return current_account
        
        # Chọn account tiếp theo
        if self.rotation_schedule == "sequential":
            next_account = self._get_next_sequential_account(active_accounts)
        else:  # random
            next_account = self._get_random_account(active_accounts)
        
        if next_account:
            self.current_account = next_account.username
            self.last_rotation = datetime.now()
            logger.info(f"Rotated to account: {next_account.username}")
        
        return next_account
    
    def _get_next_sequential_account(self, active_accounts: List[AccountInfo]) -> Optional[AccountInfo]:
        """Lấy account tiếp theo theo thứ tự"""
        if not self.current_account:
            return active_accounts[0]
        
        usernames = [acc.username for acc in active_accounts]
        
        try:
            current_index = usernames.index(self.current_account)
            next_index = (current_index + 1) % len(usernames)
            return active_accounts[next_index]
        except ValueError:
            return active_accounts[0]
    
    def _get_random_account(self, active_accounts: List[AccountInfo]) -> Optional[AccountInfo]:
        """Lấy account ngẫu nhiên"""
        # Loại bỏ account hiện tại khỏi danh sách
        available_accounts = [acc for acc in active_accounts 
                            if acc.username != self.current_account]
        
        if not available_accounts:
            available_accounts = active_accounts
        
        return random.choice(available_accounts)
    
    def update_account_activity(self, username: str, action_type: str):
        """Cập nhật hoạt động của account"""
        if username in self.accounts:
            self.accounts[username].update_activity(action_type)
            self.save_accounts()
    
    def record_account_error(self, username: str, error_message: str):
        """Ghi nhận lỗi cho account"""
        if username in self.accounts:
            self.accounts[username].record_error(error_message)
            self.save_accounts()
    
    def record_account_captcha(self, username: str):
        """Ghi nhận captcha cho account"""
        if username in self.accounts:
            self.accounts[username].record_captcha()
            self.save_accounts()
    
    def set_account_login(self, username: str):
        """Đánh dấu account đã login"""
        if username in self.accounts:
            self.accounts[username].last_login = datetime.now().isoformat()
            self.save_accounts()
    
    def get_account_stats(self, username: str) -> Dict[str, Any]:
        """Lấy thống kê account"""
        account = self.get_account(username)
        if not account:
            return {}
        
        return {
            'username': account.username,
            'total_actions': account.total_actions,
            'daily_actions': account.daily_actions,
            'likes_count': account.likes_count,
            'comments_count': account.comments_count,
            'follows_count': account.follows_count,
            'error_count': account.error_count,
            'captcha_count': account.captcha_count,
            'is_warmed_up': account.is_warmed_up,
            'last_activity': account.last_activity
        }
    
    def reset_daily_stats(self):
        """Reset thống kê hàng ngày cho tất cả accounts"""
        for account in self.accounts.values():
            account.reset_daily_stats()
        
        self.save_accounts()
        logger.info("Reset daily stats for all accounts")
    
    def deactivate_account(self, username: str, reason: str = ""):
        """Vô hiệu hóa account"""
        if username in self.accounts:
            self.accounts[username].is_active = False
            if reason:
                self.accounts[username].last_error = f"Deactivated: {reason}"
            
            self.save_accounts()
            logger.warning(f"Deactivated account {username}: {reason}")
    
    def get_total_stats(self) -> Dict[str, Any]:
        """Lấy thống kê tổng"""
        total_accounts = len(self.accounts)
        active_accounts = len(self.get_active_accounts())
        
        total_actions = sum(acc.total_actions for acc in self.accounts.values())
        total_likes = sum(acc.likes_count for acc in self.accounts.values())
        total_comments = sum(acc.comments_count for acc in self.accounts.values())
        total_follows = sum(acc.follows_count for acc in self.accounts.values())
        
        return {
            'total_accounts': total_accounts,
            'active_accounts': active_accounts,
            'total_actions': total_actions,
            'total_likes': total_likes,
            'total_comments': total_comments,
            'total_follows': total_follows
        }
