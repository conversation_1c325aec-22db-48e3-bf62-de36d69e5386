"""
Demo script cho TikTok Automation Framework
⚠️ CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC VÀ DEMO
"""

import sys
import time
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.automation_engine import TikTokAutomation
from src.account_manager import AccountManager, AccountInfo
from src.rate_limiter import RateLimiter
from src.config_manager import ConfigManager
from src.logger import setup_logger

logger = setup_logger("Demo")


def demo_config_management():
    """Demo quản lý cấu hình"""
    print("⚙️ Configuration Management Demo")
    print("-" * 40)
    
    config = ConfigManager()
    
    # Browser config
    browser_config = config.get_browser_config()
    print(f"Browser: {browser_config.type}")
    print(f"Headless: {browser_config.headless}")
    print(f"Window size: {browser_config.window_size}")
    
    # Rate limit config
    rate_config = config.get_rate_limit_config()
    print(f"Likes per hour: {rate_config.likes_per_hour}")
    print(f"Comments per hour: {rate_config.comments_per_hour}")
    print(f"Daily limit: {rate_config.daily_actions_limit}")
    
    # Safety config
    safety_config = config.get_safety_config()
    print(f"Captcha detection: {safety_config.captcha_detection}")
    print(f"Max daily errors: {safety_config.max_daily_errors}")


def demo_account_management():
    """Demo quản lý tài khoản"""
    print("\n👥 Account Management Demo")
    print("-" * 40)
    
    account_manager = AccountManager()
    
    # Add demo accounts
    demo_accounts = [
        AccountInfo(
            username="demo_user_1",
            password="demo_pass_1",
            email="<EMAIL>",
            profile_name="Demo User 1"
        ),
        AccountInfo(
            username="demo_user_2", 
            password="demo_pass_2",
            email="<EMAIL>",
            profile_name="Demo User 2"
        )
    ]
    
    for account in demo_accounts:
        if account_manager.add_account(account):
            print(f"✅ Added account: {account.username}")
    
    # Show active accounts
    active_accounts = account_manager.get_active_accounts()
    print(f"\nActive accounts: {len(active_accounts)}")
    
    # Demo rotation
    print("\nAccount rotation demo:")
    for i in range(3):
        account = account_manager.rotate_account()
        if account:
            print(f"  Rotation {i+1}: {account.username}")


def demo_rate_limiting():
    """Demo rate limiting"""
    print("\n⏱️ Rate Limiting Demo")
    print("-" * 40)
    
    config = ConfigManager()
    rate_limiter = RateLimiter(config.get_rate_limit_config())
    
    test_account = "demo_user_1"
    
    # Test action permissions
    print("Testing action permissions:")
    actions = ['like', 'comment', 'follow']
    
    for action in actions:
        can_perform = rate_limiter.can_perform_action(test_account, action)
        print(f"  Can {action}: {can_perform}")
    
    # Simulate some actions
    print("\nSimulating actions:")
    for i in range(5):
        if rate_limiter.can_perform_action(test_account, 'like'):
            rate_limiter.record_action(test_account, 'like')
            print(f"  ✅ Like action {i+1} recorded")
        else:
            print(f"  ❌ Like action {i+1} blocked by rate limit")
    
    # Show stats
    stats = rate_limiter.get_session_stats(test_account)
    print(f"\nSession stats: {stats}")


def demo_human_behavior():
    """Demo human behavior simulation"""
    print("\n🤖 Human Behavior Simulation Demo")
    print("-" * 40)
    
    # Note: This demo doesn't use actual browser
    # In real usage, this would be integrated with Selenium
    
    from src.human_behavior import HumanBehaviorSimulator
    from src.config_manager import HumanBehaviorConfig
    
    config = HumanBehaviorConfig(
        scroll_delay_min=1,
        scroll_delay_max=3,
        action_delay_min=2,
        action_delay_max=5,
        typing_speed_min=100,
        typing_speed_max=200,
        mouse_movement_enabled=True,
        mouse_randomness=0.3
    )
    
    print("Human behavior patterns:")
    print(f"  Scroll delay: {config.scroll_delay_min}-{config.scroll_delay_max}s")
    print(f"  Action delay: {config.action_delay_min}-{config.action_delay_max}s")
    print(f"  Typing speed: {config.typing_speed_min}-{config.typing_speed_max}ms/char")
    print(f"  Mouse movement: {config.mouse_movement_enabled}")
    
    # Simulate timing patterns
    print("\nSimulating reading time...")
    # This would normally use the HumanBehaviorSimulator with a real driver
    import random
    reading_time = random.uniform(2, 8)
    print(f"  Simulated reading time: {reading_time:.2f}s")


def demo_safety_features():
    """Demo tính năng an toàn"""
    print("\n🛡️ Safety Features Demo")
    print("-" * 40)
    
    config = ConfigManager()
    safety_config = config.get_safety_config()
    
    print("Safety configuration:")
    print(f"  Captcha detection: {safety_config.captcha_detection}")
    print(f"  Error retry attempts: {safety_config.error_retry_attempts}")
    print(f"  Cooldown on error: {safety_config.cooldown_on_error}s")
    print(f"  Max daily errors: {safety_config.max_daily_errors}")
    
    # Demo error handling
    account_manager = AccountManager()
    if account_manager.accounts:
        username = list(account_manager.accounts.keys())[0]
        
        print(f"\nSimulating error for account: {username}")
        account_manager.record_account_error(username, "Demo error for testing")
        
        account = account_manager.get_account(username)
        print(f"  Error count: {account.error_count}")
        print(f"  Last error: {account.last_error}")


def demo_full_workflow():
    """Demo workflow hoàn chỉnh (không chạy browser thực tế)"""
    print("\n🚀 Full Workflow Demo (Safe Mode)")
    print("-" * 40)
    
    try:
        # Initialize automation (without actually starting browser)
        print("1. Initializing automation engine...")
        automation = TikTokAutomation()
        
        print("2. Loading configuration...")
        config_stats = {
            'browser_type': automation.config.get('browser.type'),
            'rate_limits': automation.config.get('rate_limits'),
            'safety_enabled': automation.config.get('safety.captcha_detection')
        }
        print(f"   Config loaded: {config_stats}")
        
        print("3. Checking accounts...")
        active_accounts = automation.account_manager.get_active_accounts()
        print(f"   Active accounts: {len(active_accounts)}")
        
        print("4. Checking rate limits...")
        if active_accounts:
            account = active_accounts[0]
            can_like = automation.rate_limiter.can_perform_action(account.username, 'like')
            can_comment = automation.rate_limiter.can_perform_action(account.username, 'comment')
            print(f"   Can like: {can_like}, Can comment: {can_comment}")
        
        print("5. Safety checks...")
        safety_config = automation.safety_config
        print(f"   Max daily errors: {safety_config.max_daily_errors}")
        print(f"   Captcha detection: {safety_config.captcha_detection}")
        
        print("\n✅ Workflow demo completed successfully!")
        print("   (Browser automation skipped for demo purposes)")
        
    except Exception as e:
        logger.error(f"Error in workflow demo: {e}")
        print(f"❌ Error: {e}")


def main():
    """Main demo function"""
    print("🤖 TikTok Automation Framework - DEMO")
    print("⚠️  CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC!")
    print("=" * 60)
    
    try:
        demo_config_management()
        demo_account_management()
        demo_rate_limiting()
        demo_human_behavior()
        demo_safety_features()
        demo_full_workflow()
        
        print("\n🎉 All demos completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Review the generated configuration files")
        print("   2. Update config/accounts.json with real account info")
        print("   3. Install ChromeDriver for browser automation")
        print("   4. Run: python examples/basic_usage.py")
        
        print("\n⚠️  IMPORTANT REMINDERS:")
        print("   - This framework is for EDUCATIONAL purposes only")
        print("   - Always respect platform Terms of Service")
        print("   - Use responsibly and ethically")
        print("   - Test thoroughly before any real usage")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo stopped by user")
    except Exception as e:
        logger.error(f"Error in demo: {e}")
        print(f"\n❌ Demo error: {e}")


if __name__ == "__main__":
    main()
