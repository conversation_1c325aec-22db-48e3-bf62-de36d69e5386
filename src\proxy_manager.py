"""
Proxy Manager cho TikTok Automation Framework
Quản lý rotation proxy và IP management
"""

import random
import time
import requests
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from .logger import setup_logger

logger = setup_logger("ProxyManager")


@dataclass
class ProxyConfig:
    """Cấu hình proxy"""
    host: str
    port: int
    username: str = ""
    password: str = ""
    proxy_type: str = "http"  # http, https, socks4, socks5
    
    def to_dict(self) -> Dict[str, str]:
        """<PERSON>y<PERSON>n đ<PERSON>i thành dict cho requests"""
        if self.username and self.password:
            auth = f"{self.username}:{self.password}@"
        else:
            auth = ""
        
        proxy_url = f"{self.proxy_type}://{auth}{self.host}:{self.port}"
        
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def to_selenium_format(self) -> Dict[str, str]:
        """Chuyển đổi thành format cho Selenium"""
        return {
            'proxyType': 'MANUAL',
            'httpProxy': f"{self.host}:{self.port}",
            'httpsProxy': f"{self.host}:{self.port}",
            'socksProxy': f"{self.host}:{self.port}" if 'socks' in self.proxy_type else None
        }


class ProxyManager:
    """Quản lý proxy rotation và health checking"""
    
    def __init__(self, proxies: List[Dict] = None):
        self.proxies: List[ProxyConfig] = []
        self.current_proxy_index = 0
        self.proxy_health = {}  # proxy_id -> health_status
        self.rotation_interval = 300  # 5 minutes default
        self.last_rotation = 0
        
        if proxies:
            self.load_proxies(proxies)
    
    def load_proxies(self, proxy_configs: List[Dict]):
        """Load danh sách proxy từ config"""
        self.proxies = []
        
        for config in proxy_configs:
            try:
                proxy = ProxyConfig(
                    host=config['host'],
                    port=config['port'],
                    username=config.get('username', ''),
                    password=config.get('password', ''),
                    proxy_type=config.get('type', 'http')
                )
                self.proxies.append(proxy)
                self.proxy_health[len(self.proxies) - 1] = {'status': 'unknown', 'last_check': 0}
                
                logger.info(f"Loaded proxy: {proxy.host}:{proxy.port}")
                
            except Exception as e:
                logger.error(f"Error loading proxy config: {e}")
        
        logger.info(f"Loaded {len(self.proxies)} proxies")
    
    def get_current_proxy(self) -> Optional[ProxyConfig]:
        """Lấy proxy hiện tại"""
        if not self.proxies:
            return None
        
        return self.proxies[self.current_proxy_index]
    
    def rotate_proxy(self, force: bool = False) -> Optional[ProxyConfig]:
        """Rotate sang proxy tiếp theo"""
        if not self.proxies:
            return None
        
        current_time = time.time()
        
        # Kiểm tra có cần rotate không
        if not force and (current_time - self.last_rotation) < self.rotation_interval:
            return self.get_current_proxy()
        
        # Tìm proxy healthy tiếp theo
        attempts = 0
        max_attempts = len(self.proxies)
        
        while attempts < max_attempts:
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
            proxy = self.proxies[self.current_proxy_index]
            
            # Kiểm tra health của proxy
            if self._is_proxy_healthy(self.current_proxy_index):
                self.last_rotation = current_time
                logger.info(f"Rotated to proxy: {proxy.host}:{proxy.port}")
                return proxy
            
            attempts += 1
        
        # Nếu không có proxy healthy nào, dùng proxy đầu tiên
        logger.warning("No healthy proxies found, using first proxy")
        self.current_proxy_index = 0
        self.last_rotation = current_time
        return self.proxies[0] if self.proxies else None
    
    def _is_proxy_healthy(self, proxy_index: int) -> bool:
        """Kiểm tra proxy có healthy không"""
        if proxy_index not in self.proxy_health:
            return False
        
        health_info = self.proxy_health[proxy_index]
        
        # Nếu chưa check hoặc check cũ hơn 1 giờ
        current_time = time.time()
        if (health_info['status'] == 'unknown' or 
            current_time - health_info['last_check'] > 3600):
            return self._check_proxy_health(proxy_index)
        
        return health_info['status'] == 'healthy'
    
    def _check_proxy_health(self, proxy_index: int) -> bool:
        """Kiểm tra health của proxy"""
        if proxy_index >= len(self.proxies):
            return False
        
        proxy = self.proxies[proxy_index]
        
        try:
            # Test proxy với request đơn giản
            test_urls = [
                'http://httpbin.org/ip',
                'https://api.ipify.org?format=json',
                'http://ip-api.com/json'
            ]
            
            for url in test_urls:
                try:
                    response = requests.get(
                        url,
                        proxies=proxy.to_dict(),
                        timeout=10,
                        headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                    )
                    
                    if response.status_code == 200:
                        self.proxy_health[proxy_index] = {
                            'status': 'healthy',
                            'last_check': time.time(),
                            'response_time': response.elapsed.total_seconds()
                        }
                        logger.debug(f"Proxy {proxy.host}:{proxy.port} is healthy")
                        return True
                        
                except requests.RequestException:
                    continue
            
            # Nếu tất cả test URLs đều fail
            self.proxy_health[proxy_index] = {
                'status': 'unhealthy',
                'last_check': time.time()
            }
            logger.warning(f"Proxy {proxy.host}:{proxy.port} is unhealthy")
            return False
            
        except Exception as e:
            logger.error(f"Error checking proxy health: {e}")
            self.proxy_health[proxy_index] = {
                'status': 'unhealthy',
                'last_check': time.time()
            }
            return False
    
    def get_proxy_for_selenium(self) -> Optional[Dict]:
        """Lấy proxy config cho Selenium"""
        proxy = self.get_current_proxy()
        if not proxy:
            return None
        
        return proxy.to_selenium_format()
    
    def get_proxy_for_requests(self) -> Optional[Dict]:
        """Lấy proxy config cho requests"""
        proxy = self.get_current_proxy()
        if not proxy:
            return None
        
        return proxy.to_dict()
    
    def check_all_proxies(self):
        """Kiểm tra health của tất cả proxy"""
        logger.info("Checking health of all proxies...")
        
        healthy_count = 0
        for i in range(len(self.proxies)):
            if self._check_proxy_health(i):
                healthy_count += 1
        
        logger.info(f"Health check complete: {healthy_count}/{len(self.proxies)} proxies healthy")
    
    def get_proxy_stats(self) -> Dict:
        """Lấy thống kê proxy"""
        if not self.proxies:
            return {'total': 0, 'healthy': 0, 'unhealthy': 0, 'unknown': 0}
        
        stats = {'total': len(self.proxies), 'healthy': 0, 'unhealthy': 0, 'unknown': 0}
        
        for health_info in self.proxy_health.values():
            status = health_info.get('status', 'unknown')
            stats[status] += 1
        
        return stats
    
    def get_random_proxy(self) -> Optional[ProxyConfig]:
        """Lấy proxy ngẫu nhiên (healthy)"""
        if not self.proxies:
            return None
        
        healthy_proxies = []
        for i, proxy in enumerate(self.proxies):
            if self._is_proxy_healthy(i):
                healthy_proxies.append((i, proxy))
        
        if not healthy_proxies:
            # Nếu không có proxy healthy, trả về random
            index = random.randint(0, len(self.proxies) - 1)
            self.current_proxy_index = index
            return self.proxies[index]
        
        # Chọn random từ healthy proxies
        index, proxy = random.choice(healthy_proxies)
        self.current_proxy_index = index
        return proxy
    
    def mark_proxy_failed(self, proxy_index: int = None):
        """Đánh dấu proxy hiện tại hoặc chỉ định là failed"""
        if proxy_index is None:
            proxy_index = self.current_proxy_index
        
        if proxy_index in self.proxy_health:
            self.proxy_health[proxy_index]['status'] = 'unhealthy'
            self.proxy_health[proxy_index]['last_check'] = time.time()
            
            proxy = self.proxies[proxy_index]
            logger.warning(f"Marked proxy as failed: {proxy.host}:{proxy.port}")
    
    def set_rotation_interval(self, seconds: int):
        """Thiết lập interval cho proxy rotation"""
        self.rotation_interval = seconds
        logger.info(f"Proxy rotation interval set to {seconds} seconds")


class NoProxyManager(ProxyManager):
    """Proxy manager giả cho trường hợp không dùng proxy"""
    
    def __init__(self):
        super().__init__([])
    
    def get_current_proxy(self) -> None:
        return None
    
    def rotate_proxy(self, force: bool = False) -> None:
        return None
    
    def get_proxy_for_selenium(self) -> None:
        return None
    
    def get_proxy_for_requests(self) -> None:
        return None
