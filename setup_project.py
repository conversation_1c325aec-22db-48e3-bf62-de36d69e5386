"""
<PERSON><PERSON><PERSON> thiết lập dự án TikTok Automation Framework
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime


def create_directories():
    """<PERSON><PERSON><PERSON> các thư mục cần thiết"""
    directories = [
        "logs",
        "data", 
        "browser_profiles",
        "docs",
        "examples",
        "tests"
    ]
    
    print("📁 Creating directories...")
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ {directory}/")


def setup_config_files():
    """Thiết lập file cấu hình"""
    print("\n⚙️ Setting up configuration files...")
    
    # Copy example settings if settings.yaml doesn't exist
    settings_file = Path("config/settings.yaml")
    example_file = Path("config/settings.example.yaml")
    
    if not settings_file.exists() and example_file.exists():
        shutil.copy(example_file, settings_file)
        print("   ✅ config/settings.yaml created from example")
    
    # Create accounts.json if it doesn't exist
    accounts_file = Path("config/accounts.json")
    if not accounts_file.exists():
        example_accounts = {
            "example_user": {
                "username": "your_username_here",
                "password": "your_password_here", 
                "email": "<EMAIL>",
                "phone": "",
                "profile_name": "Your Profile Name",
                "is_active": False,  # Set to False by default for safety
                "is_verified": False,
                "is_warmed_up": False,
                "created_date": datetime.now().isoformat(),
                "last_login": "",
                "last_activity": "",
                "warm_up_start": "",
                "total_actions": 0,
                "daily_actions": 0,
                "likes_count": 0,
                "comments_count": 0,
                "follows_count": 0,
                "error_count": 0,
                "captcha_count": 0,
                "last_error": "",
                "browser_profile_path": "",
                "user_agent": ""
            }
        }
        
        with open(accounts_file, 'w', encoding='utf-8') as f:
            json.dump(example_accounts, f, indent=2, ensure_ascii=False)
        
        print("   ✅ config/accounts.json created")
        print("   ⚠️  Please update accounts.json with your actual account information")


def create_env_file():
    """Tạo file .env"""
    print("\n🔐 Creating environment file...")
    
    env_file = Path(".env")
    if not env_file.exists():
        env_content = """# TikTok Automation Framework Environment Variables

# Logging
LOG_LEVEL=INFO
LOG_DIR=logs

# Browser
BROWSER_TYPE=chrome
HEADLESS=false

# Safety
MAX_DAILY_ERRORS=10
CAPTCHA_DETECTION=true

# Rate Limiting
LIKES_PER_HOUR=30
COMMENTS_PER_HOUR=10
FOLLOWS_PER_HOUR=20

# Proxy (optional)
PROXY_ENABLED=false
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# Development
DEBUG=false
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("   ✅ .env file created")


def create_documentation():
    """Tạo documentation cơ bản"""
    print("\n📚 Creating documentation...")
    
    docs_dir = Path("docs")
    
    # Installation guide
    install_guide = docs_dir / "INSTALLATION.md"
    with open(install_guide, 'w', encoding='utf-8') as f:
        f.write("""# Hướng dẫn Cài đặt

## Yêu cầu Hệ thống

- Python 3.8+
- Chrome hoặc Firefox browser
- ChromeDriver hoặc GeckoDriver

## Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd TikTok
```

2. Tạo virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\\Scripts\\activate  # Windows
```

3. Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

4. Thiết lập dự án:
```bash
python setup_project.py
```

5. Cấu hình accounts:
- Chỉnh sửa `config/accounts.json`
- Cập nhật `config/settings.yaml` nếu cần

## Chạy Tests

```bash
python run_tests.py
```

## Sử dụng

```bash
python examples/basic_usage.py
```
""")
    
    # Usage guide
    usage_guide = docs_dir / "USAGE.md"
    with open(usage_guide, 'w', encoding='utf-8') as f:
        f.write("""# Hướng dẫn Sử dụng

⚠️ **CẢNH BÁO**: Framework này chỉ dành cho mục đích giáo dục!

## Cấu hình Cơ bản

### 1. Cấu hình Accounts

Chỉnh sửa `config/accounts.json`:

```json
{
  "your_username": {
    "username": "your_username",
    "password": "your_password",
    "email": "<EMAIL>",
    "is_active": true
  }
}
```

### 2. Cấu hình Settings

Chỉnh sửa `config/settings.yaml`:

```yaml
browser:
  type: "chrome"
  headless: false

rate_limits:
  likes_per_hour: 30
  comments_per_hour: 10
```

## Sử dụng Cơ bản

```python
from src.automation_engine import TikTokAutomation

# Khởi tạo
automation = TikTokAutomation()

# Chạy safe mode
automation.run_safe_mode(duration_minutes=10)
```

## Tính năng An toàn

- Rate limiting tự động
- Captcha detection
- Error handling
- Session management
- Human behavior simulation

## Tuân thủ Đạo đức

- Không spam
- Tôn trọng rate limits
- Không tạo nội dung có hại
- Tuân thủ ToS của platform
""")
    
    print("   ✅ docs/INSTALLATION.md created")
    print("   ✅ docs/USAGE.md created")


def check_dependencies():
    """Kiểm tra dependencies"""
    print("\n🔍 Checking dependencies...")
    
    try:
        import selenium
        print("   ✅ Selenium installed")
    except ImportError:
        print("   ❌ Selenium not found - run: pip install selenium")
    
    try:
        import yaml
        print("   ✅ PyYAML installed")
    except ImportError:
        print("   ❌ PyYAML not found - run: pip install pyyaml")
    
    try:
        import requests
        print("   ✅ Requests installed")
    except ImportError:
        print("   ❌ Requests not found - run: pip install requests")


def show_next_steps():
    """Hiển thị các bước tiếp theo"""
    print("\n🎉 Project setup completed!")
    print("\n📋 Next steps:")
    print("   1. Update config/accounts.json with your account information")
    print("   2. Review and modify config/settings.yaml if needed")
    print("   3. Install ChromeDriver or GeckoDriver")
    print("   4. Run tests: python run_tests.py")
    print("   5. Try basic example: python examples/basic_usage.py")
    print("\n⚠️  IMPORTANT:")
    print("   - This framework is for EDUCATIONAL purposes only")
    print("   - Always respect platform Terms of Service")
    print("   - Use responsibly and ethically")
    print("\n📚 Documentation:")
    print("   - Installation: docs/INSTALLATION.md")
    print("   - Usage: docs/USAGE.md")


def main():
    """Main setup function"""
    print("🤖 TikTok Automation Framework Setup")
    print("⚠️  CHỈ DÀNH CHO MỤC ĐÍCH GIÁO DỤC!")
    print("=" * 50)
    
    try:
        create_directories()
        setup_config_files()
        create_env_file()
        create_documentation()
        check_dependencies()
        show_next_steps()
        
    except Exception as e:
        print(f"\n❌ Error during setup: {e}")
        return False
    
    return True


if __name__ == "__main__":
    main()
