# TikTok Automation Framework (G<PERSON><PERSON><PERSON> dụ<PERSON>)

⚠️ **CẢNH BÁO QUAN TRỌNG**: Framework này chỉ dành cho mục đích giáo dục và nghiên cứu. Việc sử dụng bot tự động có thể vi phạm Điều khoản Dịch vụ của TikTok.

## Tính năng

- 🤖 Web automation với Selenium/Playwright
- 👤 Mô phỏng hành vi người dùng thực tế
- 🔄 Quản lý nhiều tài khoản
- 🌐 Proxy rotation và IP management
- ⏱️ Rate limiting thông minh
- 📊 Logging và monitoring chi tiết
- 🛡️ Tính năng bảo mật và error handling

## Cài đặt

```bash
pip install -r requirements.txt
```

## Cấu hình

1. Sao chép file cấu hình mẫu:
```bash
cp config/settings.example.yaml config/settings.yaml
```

2. Chỉnh sửa cấu hình trong `config/settings.yaml`

3. Thêm thông tin tài khoản vào `config/accounts.yaml`

## Sử dụng

```python
from src.automation_engine import TikTokAutomation

# Khởi tạo automation engine
automation = TikTokAutomation()

# Chạy automation với cấu hình an toàn
automation.run_safe_mode()
```

## Cấu trúc dự án

```
├── src/                    # Mã nguồn chính
├── config/                 # File cấu hình
├── logs/                   # File log
├── tests/                  # Unit tests
├── docs/                   # Tài liệu
└── examples/               # Ví dụ sử dụng
```

## Đạo đức và Tuân thủ

- Luôn tuân thủ Điều khoản Dịch vụ của platform
- Không spam hoặc tạo nội dung có hại
- Sử dụng rate limiting hợp lý
- Tôn trọng quyền riêng tư của người dùng khác

## Giấy phép

MIT License - Chỉ dành cho mục đích giáo dục
